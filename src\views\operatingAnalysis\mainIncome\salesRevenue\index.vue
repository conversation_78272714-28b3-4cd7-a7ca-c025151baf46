<template>
  <div class="salesRevenue">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分油气田统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="trend-box">
        <chartBox :title="'分终端日销量趋势'"></chartBox>
      </div>
      <div class="completion-rate">
        <chartBox :title="'同比/环比增减动因'"></chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CarouselBtn from "../../components/CarouselBtn.vue";
import ItemCard from "../../components/ItemCard.vue";
import CommonTable from "@/components/comTable/commonTable.vue";

export default {
  name: "salesRevenue",
  components: {
    CarouselBtn,
    ItemCard,
    CommonTable,
  },
  data() {
    return {
      newDateValue: "",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "油气合计收入", value: "10000" },
        { title: "天然气收入", value: "10000" },
        { title: "凝析油收入", value: "10000" },
        { title: "原油收入", value: "10000" },
      ],
      colums: [
        { label: "作业公司", prop: "company" },
        { label: "油气类型", prop: "type" },
        { label: "本年累计", prop: "year" },
        { label: "同期预算目标", prop: "budget" },
        { label: "同期预算完成率", prop: "budgetRate" },
        { label: "全年预算目标", prop: "yearBudget" },
        { label: "全年预算完成率", prop: "yearBudgetRate" },
      ],
      tableData: [
        {
          company: "作业公司1",
          type: "油气",
          year: "10000",
          budget: "10000",
          budgetRate: "100%",
          yearBudget: "10000",
          yearBudgetRate: "100%",
        },
        {
          company: "作业公司2",
          type: "油气",
          year: "10000",
          budget: "10000",
          budgetRate: "100%",
          yearBudget: "10000",
          yearBudgetRate: "100%",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.salesRevenue {
  .content-up {
    display: flex;
    gap: 10px; // 使用gap替代margin，确保间距一致
    .main-indicators {
      flex: 1;
      min-width: 0; // 添加min-width: 0，允许flex收缩
      display: flex;
      flex-direction: column;
    }
    .statistics-box {
      flex: 1;
      min-width: 0; // 确保两个容器都有相同的收缩行为
      display: flex;
      flex-direction: column;

      .table-box {
        margin: 12px 16px;
        flex: 1;
        overflow: hidden; // 修改：隐藏溢出，让表格内部处理滚动
        display: flex;
        flex-direction: column;
        min-height: 0; // 允许flex收缩
      }
    }

    .card-box {
      display: flex;
      justify-content: space-between;
      margin: 12px 16px; // 与table-box保持相同的边距
      flex: 1; // 占据剩余空间
      min-height: 0; // 允许flex收缩
      gap: 12px; // 使用gap替代margin-right

      .item-card {
        flex: 1;
        min-width: 0; // 允许收缩
        height: 100%; // 确保卡片填满容器高度
      }
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    .trend-box {
      flex: 1;
    }

    .completion-rate {
      flex: 1;
      margin: 0 10px;
    }
  }
}
</style>
